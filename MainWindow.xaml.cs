using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Controls.Primitives; // 添加这个命名空间解决StatusBar问题

namespace AIGCN
{
    public partial class MainWindow : Window
    {
        // UI控件字段
        private RichTextBox? mainContent;
        private TextBox? inputTextBox;
        
        // API配置
        private const string BaseUrl = "https://api.siliconflow.cn/v1";
        private const string DefaultModel = "deepseek-ai/DeepSeek-V3";
        private string _apiKey = "";
        
        // 应用状态
        private List<Message> _conversationHistory = new List<Message>();
        private string _currentStyle = "古风仙侠";
        private string _currentCharacter = "天才修士";
        private string _currentSetting = "修真世界";

        public MainWindow()
        {
            // 不使用InitializeComponent()方法
            InitUI();
            LoadDefaultPrompts();
        }

        private void InitUI()
        {
            // 设置窗口样式
            this.Background = new SolidColorBrush(Color.FromRgb(30, 30, 50));
            this.Foreground = Brushes.White;
            this.Title = "幻梦小说创作工坊 - AI助手";
            this.Width = 1000;
            this.Height = 700;

            // 创建主布局
            var mainGrid = new Grid();
            mainGrid.ColumnDefinitions.Add(new ColumnDefinition() { Width = new GridLength(250) });
            mainGrid.ColumnDefinitions.Add(new ColumnDefinition() { Width = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition() { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition() { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition() { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition() { Height = GridLength.Auto });

            // API设置区域
            var apiPanel = CreateApiPanel();
            Grid.SetColumnSpan(apiPanel, 2);
            mainGrid.Children.Add(apiPanel);

            // 侧边控制区
            var sidebar = CreateSidebar();
            Grid.SetRow(sidebar, 1);
            mainGrid.Children.Add(sidebar);

            // 主创作区
            var creatorPanel = CreateCreatorPanel();
            Grid.SetColumn(creatorPanel, 1);
            Grid.SetRow(creatorPanel, 1);
            mainGrid.Children.Add(creatorPanel);

            // 输入区域
            var inputPanel = CreateInputPanel();
            Grid.SetColumn(inputPanel, 0);
            Grid.SetColumnSpan(inputPanel, 2);
            Grid.SetRow(inputPanel, 2);
            mainGrid.Children.Add(inputPanel);

            // 状态栏 - 使用Grid代替StatusBar
            var statusGrid = new Grid
            {
                Background = new SolidColorBrush(Color.FromRgb(50, 50, 70)),
                Height = 30
            };
            statusGrid.ColumnDefinitions.Add(new ColumnDefinition() { Width = new GridLength(1, GridUnitType.Star) });
            statusGrid.ColumnDefinitions.Add(new ColumnDefinition() { Width = GridLength.Auto });
            
            var statusText = new TextBlock
            {
                Text = "就绪",
                Foreground = Brushes.White,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 10, 0)
            };
            
            var progress = new ProgressBar
            {
                Width = 100,
                Height = 15,
                IsIndeterminate = false,
                Visibility = Visibility.Collapsed,
                Margin = new Thickness(0, 0, 10, 0)
            };
            
            Grid.SetColumn(statusText, 0);
            Grid.SetColumn(progress, 1);
            
            statusGrid.Children.Add(statusText);
            statusGrid.Children.Add(progress);
            
            Grid.SetColumnSpan(statusGrid, 2);
            Grid.SetRow(statusGrid, 3);
            mainGrid.Children.Add(statusGrid);

            this.Content = mainGrid;
        }

        private Grid CreateApiPanel()
        {
            // 使用Grid替代StackPanel
            var grid = new Grid
            {
                Background = new SolidColorBrush(Color.FromRgb(50, 50, 70)),
            };
            grid.ColumnDefinitions.Add(new ColumnDefinition() { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition() { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition() { Width = GridLength.Auto });
            grid.ColumnDefinitions.Add(new ColumnDefinition() { Width = new GridLength(1, GridUnitType.Star) });
            
            var keyLabel = new Label { 
                Content = "API Key:", 
                Foreground = Brushes.LightCyan, 
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 5, 0)
            };
            
            var keyBox = new PasswordBox { 
                Width = 250, 
                Margin = new Thickness(5), 
                VerticalAlignment = VerticalAlignment.Center 
            };
            keyBox.PasswordChanged += (s, e) => _apiKey = keyBox.Password;

            var modelLabel = new Label { 
                Content = "模型:", 
                Margin = new Thickness(20, 0, 5, 0), 
                VerticalAlignment = VerticalAlignment.Center 
            };
            
            var modelCombo = new ComboBox
            {
                Width = 150,
                ItemsSource = new[] { "deepseek-ai/DeepSeek-V3", "gpt-3.5-turbo", "gpt-4" },
                SelectedIndex = 0,
                Margin = new Thickness(5),
                VerticalAlignment = VerticalAlignment.Center
            };

            Grid.SetColumn(keyLabel, 0);
            Grid.SetColumn(keyBox, 1);
            Grid.SetColumn(modelLabel, 2);
            Grid.SetColumn(modelCombo, 3);
            
            grid.Children.Add(keyLabel);
            grid.Children.Add(keyBox);
            grid.Children.Add(modelLabel);
            grid.Children.Add(modelCombo);

            return grid;
        }

        private Border CreateSidebar()
        {
            // 使用Border提供Padding效果
            var border = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(40, 40, 60)),
                BorderBrush = Brushes.Transparent,
                Padding = new Thickness(15),
                Child = new StackPanel()
            };
            
            var panel = (StackPanel)border.Child;

            // 预设按钮
            var presets = new[] { "人物设定", "情节构思", "世界观", "文风调整", "章节大纲" };
            foreach (var preset in presets)
            {
                var btn = new Button
                {
                    Content = preset,
                    Margin = new Thickness(0, 5, 0, 5),
                    Background = new SolidColorBrush(Color.FromRgb(65, 105, 225)),
                    Foreground = Brushes.White,
                    Padding = new Thickness(5)
                };
                btn.Click += (s, e) => PresetButton_Click(preset);
                panel.Children.Add(btn);
            }

            // 历史记录
            panel.Children.Add(new Label 
            { 
                Content = "创作历史", 
                Foreground = Brushes.Cyan,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 20, 0, 5)
            });

            var historyList = new ListBox
            {
                Background = new SolidColorBrush(Color.FromRgb(50, 50, 70)),
                Foreground = Brushes.White,
                Height = 200
            };
            historyList.Items.Add("第一章：仙途启程");
            historyList.Items.Add("第二章：秘境奇遇");
            panel.Children.Add(historyList);

            return border;
        }

        private RichTextBox CreateCreatorPanel()
        {
            mainContent = new RichTextBox
            {
                Background = new SolidColorBrush(Color.FromRgb(30, 30, 40)),
                Foreground = Brushes.White,
                Margin = new Thickness(15), // 使用Margin替代Padding
                IsReadOnly = false,
                FontFamily = new FontFamily("Microsoft YaHei"),
                FontSize = 14,
                BorderThickness = new Thickness(0)
            };

            var paragraph = new Paragraph();
            paragraph.Inlines.Add("欢迎使用幻梦小说创作工坊！\n\n请先输入API Key，然后开始您的创作之旅。\n您可以使用侧边栏预设模板快速生成内容，或在下方输入框直接与AI对话。");
            mainContent.Document.Blocks.Add(paragraph);

            return mainContent;
        }

        private DockPanel CreateInputPanel()
        {
            // 使用DockPanel替代StackPanel
            var dockPanel = new DockPanel
            {
                Background = new SolidColorBrush(Color.FromRgb(50, 50, 70)),
            };
            
            inputTextBox = new TextBox
            {
                MinWidth = 400,
                Height = 80,
                Margin = new Thickness(10),
                AcceptsReturn = true,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                FontSize = 14
            };
            DockPanel.SetDock(inputTextBox, Dock.Left);
            
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };
            
            var sendButton = new Button
            {
                Content = "生成内容",
                Margin = new Thickness(5),
                Padding = new Thickness(10, 5, 10, 5),
                Background = new SolidColorBrush(Color.FromRgb(65, 105, 225)),
                Foreground = Brushes.White
            };
            sendButton.Click += async (s, e) => await SendToAI(inputTextBox.Text);
            
            var saveButton = new Button
            {
                Content = "保存章节",
                Margin = new Thickness(5),
                Padding = new Thickness(10, 5, 10, 5),
                Background = new SolidColorBrush(Color.FromRgb(65, 105, 225)),
                Foreground = Brushes.White,
                ToolTip = "将当前内容保存为小说章节"
            };
            
            buttonPanel.Children.Add(sendButton);
            buttonPanel.Children.Add(saveButton);
            DockPanel.SetDock(buttonPanel, Dock.Right);
            
            dockPanel.Children.Add(inputTextBox);
            dockPanel.Children.Add(buttonPanel);

            return dockPanel;
        }

        private void LoadDefaultPrompts()
        {
            _conversationHistory.Add(new Message
            {
                Role = "system",
                Content = $"你是一位资深小说创作助手，当前创作方向：{_currentStyle}风格，" +
                          $"主角设定：{_currentCharacter}，世界观背景：{_currentSetting}。" +
                          "请以优美流畅的文学语言进行创作，注意情节的连贯性和人物塑造。"
            });
        }

        private void PresetButton_Click(string presetType)
        {
            string presetPrompt = "";
            switch (presetType)
            {
                case "人物设定":
                    presetPrompt = "请根据当前风格创作一个立体丰满的角色设定卡，包括姓名、外貌、性格、特殊能力和背景故事：";
                    break;
                case "情节构思":
                    presetPrompt = "构思一个包含冲突、转折和高潮的精彩情节，约500字：";
                    break;
                case "世界观":
                    presetPrompt = "扩展世界观设定，描述这个世界的独特规则、地理特征和重要势力：";
                    break;
                case "文风调整":
                    presetPrompt = "请将当前文本转换为更具文学性的表达，使用更丰富的修辞手法：";
                    break;
                case "章节大纲":
                    presetPrompt = "为当前故事设计一个包含起承转合的完整章节大纲：";
                    break;
            }
            
            inputTextBox.Text = presetPrompt;
            inputTextBox.Focus();
            inputTextBox.SelectAll();
        }

        private async Task SendToAI(string userInput)
        {
            if (string.IsNullOrWhiteSpace(_apiKey))
            {
                MessageBox.Show("请先输入API Key", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(userInput))
            {
                MessageBox.Show("请输入要生成的内容", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // 添加到对话历史
            _conversationHistory.Add(new Message { Role = "user", Content = userInput });
            inputTextBox.IsEnabled = false;

            try
            {
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
                    client.Timeout = TimeSpan.FromSeconds(60);

                    var request = new
                    {
                        model = DefaultModel,
                        messages = _conversationHistory,
                        max_tokens = 2000,
                        temperature = 0.7
                    };

                    var json = JsonSerializer.Serialize(request);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    var response = await client.PostAsync($"{BaseUrl}/chat/completions", content);
                    var responseString = await response.Content.ReadAsStringAsync();

                    if (!response.IsSuccessStatusCode)
                    {
                        MessageBox.Show($"API错误: {response.StatusCode}\n{responseString}", "API错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    var responseObject = JsonDocument.Parse(responseString);
                    var aiResponse = responseObject.RootElement
                        .GetProperty("choices")[0]
                        .GetProperty("message")
                        .GetProperty("content")
                        .GetString() ?? "未获取到响应内容";

                    // 添加到对话历史
                    _conversationHistory.Add(new Message { Role = "assistant", Content = aiResponse });

                    // 更新UI显示
                    DisplayText(aiResponse);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"请求失败: {ex.Message}", "网络错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                inputTextBox.Clear();
                inputTextBox.IsEnabled = true;
                inputTextBox.Focus();
            }
        }

        private void DisplayText(string text)
        {
            if (string.IsNullOrEmpty(text)) return;

            var paragraph = new Paragraph();
            paragraph.Inlines.Add(text);
            mainContent.Document.Blocks.Add(paragraph);
            mainContent.ScrollToEnd();
            
            // 添加分隔线
            var separator = new Paragraph();
            separator.Inlines.Add(new Run("────────────────────────"));
            separator.Foreground = Brushes.Gray;
            mainContent.Document.Blocks.Add(separator);
        }

        // 添加缺失的事件处理方法
        private void Window_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            this.DragMove();
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (this.WindowState == WindowState.Maximized)
                this.WindowState = WindowState.Normal;
            else
                this.WindowState = WindowState.Maximized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private async void GenerateButton_Click(object sender, RoutedEventArgs e)
        {
            if (inputTextBox != null)
            {
                await SendToAI(inputTextBox.Text);
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            if (mainContent != null)
            {
                mainContent.Document.Blocks.Clear();
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (mainContent == null) return;

            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*",
                DefaultExt = "txt",
                FileName = $"AI小说_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    var textRange = new TextRange(mainContent.Document.ContentStart, mainContent.Document.ContentEnd);
                    File.WriteAllText(saveFileDialog.FileName, textRange.Text, System.Text.Encoding.UTF8);
                    MessageBox.Show("保存成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"保存失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void ClearHistoryButton_Click(object sender, RoutedEventArgs e)
        {
            _conversationHistory.Clear();
            if (mainContent != null)
            {
                mainContent.Document.Blocks.Clear();
            }
        }
    }

    public class Message
    {
        public string Role { get; set; } = "";
        public string Content { get; set; } = "";
    }
}