<Window x:Class="AIGCN.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:AIGCN"
        mc:Ignorable="d"
        Title="AI小说创作助手" Height="800" Width="1200"
        WindowStyle="None" AllowsTransparency="True" 
        Background="Transparent" ResizeMode="CanResize"
        MouseLeftButtonDown="Window_MouseLeftButtonDown">
    
    <Window.Resources>
        <LinearGradientBrush x:Key="BackgroundGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#1a1a2e" Offset="0"/>
            <GradientStop Color="#16213e" Offset="0.5"/>
            <GradientStop Color="#0f3460" Offset="1"/>
        </LinearGradientBrush>
        
        <LinearGradientBrush x:Key="CardGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#ffffff" Offset="0"/>
            <GradientStop Color="#f8f9fa" Offset="1"/>
        </LinearGradientBrush>
        
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#007acc"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="8" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005a9e"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004080"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#e0e0e0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"
                                        VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#007acc"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Border Background="{StaticResource BackgroundGradient}" CornerRadius="10">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 标题栏 -->
            <Border Grid.Row="0" Background="#1a1a2e" CornerRadius="10,10,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="AI小说创作助手" 
                               Foreground="White" FontSize="18" FontWeight="Bold"
                               VerticalAlignment="Center" Margin="20,0"/>
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,10,0">
                        <Button x:Name="minimizeButton" Content="─" Width="30" Height="30"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Click="MinimizeButton_Click" Margin="5,0"/>
                        <Button x:Name="maximizeButton" Content="□" Width="30" Height="30"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Click="MaximizeButton_Click" Margin="5,0"/>
                        <Button x:Name="closeButton" Content="×" Width="30" Height="30"
                                Background="Transparent" Foreground="White" BorderThickness="0"
                                Click="CloseButton_Click" Margin="5,0"/>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- 主内容区域 -->
            <Grid Grid.Row="1" Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="10"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 左侧：输入和输出区域 -->
                <Grid Grid.Column="0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- API密钥输入 -->
                    <Border Grid.Row="0" Background="{StaticResource CardGradient}" 
                            CornerRadius="8" Margin="0,0,0,15" Padding="15">
                        <StackPanel>
                            <TextBlock Text="API密钥" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                            <TextBox x:Name="apiKeyTextBox" Style="{StaticResource ModernTextBox}"
                                     Height="40" FontFamily="Consolas"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- 模型和参数设置 -->
                    <Border Grid.Row="1" Background="{StaticResource CardGradient}" 
                            CornerRadius="8" Margin="0,0,0,15" Padding="15">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.ColumnSpan="2" Text="AI模型设置" 
                                       FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>
                            
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="模型选择" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="modelComboBox" Height="35" FontSize="14">
                                    <ComboBoxItem Content="deepseek-ai/DeepSeek-V3" IsSelected="True"/>
                                    <ComboBoxItem Content="deepseek-ai/DeepSeek-Coder"/>
                                    <ComboBoxItem Content="deepseek-ai/DeepSeek-Math"/>
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="温度 (创造性)" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <Slider x:Name="temperatureSlider" Minimum="0" Maximum="2" Value="0.7" 
                                        TickFrequency="0.1" IsSnapToTickEnabled="True"/>
                                <TextBlock Text="{Binding ElementName=temperatureSlider, Path=Value, StringFormat='{}{0:F1}'}" 
                                           HorizontalAlignment="Center" FontSize="12"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,15,0,0">
                                <TextBlock Text="最大Token数" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <Slider x:Name="maxTokensSlider" Minimum="100" Maximum="4000" Value="1000" 
                                        TickFrequency="100" IsSnapToTickEnabled="True"/>
                                <TextBlock Text="{Binding ElementName=maxTokensSlider, Path=Value, StringFormat='{}{0}'}" 
                                           HorizontalAlignment="Center" FontSize="12"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                    
                    <!-- 创作提示输入 -->
                    <Border Grid.Row="2" Background="{StaticResource CardGradient}" 
                            CornerRadius="8" Margin="0,0,0,15" Padding="15">
                        <StackPanel>
                            <TextBlock Text="创作提示" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                            <TextBox x:Name="promptTextBox" Style="{StaticResource ModernTextBox}"
                                     Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                     VerticalScrollBarVisibility="Auto"
                                     FontSize="14"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- 生成结果 -->
                    <Border Grid.Row="3" Background="{StaticResource CardGradient}" 
                            CornerRadius="8" Margin="0,0,0,15" Padding="15">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Text="生成结果" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                            <TextBox x:Name="resultTextBox" Grid.Row="1" Style="{StaticResource ModernTextBox}"
                                     TextWrapping="Wrap" AcceptsReturn="True" IsReadOnly="True"
                                     VerticalScrollBarVisibility="Auto" FontSize="14"
                                     Background="#f8f9fa"/>
                        </Grid>
                    </Border>
                    
                    <!-- 操作按钮 -->
                    <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="generateButton" Content="生成小说内容" Style="{StaticResource ModernButton}"
                                Click="GenerateButton_Click" Margin="0,0,10,0"/>
                        <Button x:Name="clearButton" Content="清空结果" Style="{StaticResource ModernButton}"
                                Background="#6c757d" Click="ClearButton_Click" Margin="0,0,10,0"/>
                        <Button x:Name="saveButton" Content="保存文件" Style="{StaticResource ModernButton}"
                                Background="#28a745" Click="SaveButton_Click"/>
                    </StackPanel>
                </Grid>
                
                <!-- 右侧：历史记录 -->
                <Border Grid.Column="2" Background="{StaticResource CardGradient}" 
                        CornerRadius="8" Padding="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Text="历史记录" FontWeight="Bold" FontSize="16" Margin="0,0,0,10"/>
                        
                        <TextBox x:Name="historyTextBox" Grid.Row="1" Style="{StaticResource ModernTextBox}"
                                 TextWrapping="Wrap" AcceptsReturn="True" IsReadOnly="True"
                                 VerticalScrollBarVisibility="Auto" FontSize="12"
                                 Background="#f8f9fa"/>
                        
                        <Button Grid.Row="2" x:Name="clearHistoryButton" Content="清空历史" 
                                Style="{StaticResource ModernButton}" Background="#dc3545"
                                Click="ClearHistoryButton_Click" Margin="0,10,0,0"/>
                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </Border>
</Window>