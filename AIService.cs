using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.Net.Http.Headers;

namespace AIGCN
{
    public class AIService
    {
        private readonly string _apiKey;
        private const string BaseUrl = "https://api.siliconflow.cn/v1";
        private const string DefaultModel = "deepseek-ai/DeepSeek-V3";

        public AIService(string apiKey)
        {
            _apiKey = apiKey;
        }

        public async Task<string> GenerateTextAsync(string prompt, string? model = null, double temperature = 0.7, int maxTokens = 1000)
        {
            using (var client = new HttpClient())
            {
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

                var requestBody = new
                {
                    model = model is not null ? model : DefaultModel,
                    prompt = prompt,
                    max_tokens = maxTokens,
                    temperature = temperature,
                    top_p = 0.9,
                    frequency_penalty = 0.0,
                    presence_penalty = 0.0
                };

                var json = JsonConvert.SerializeObject(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await client.PostAsync($"{BaseUrl}/completions", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseString = await response.Content.ReadAsStringAsync();
                    dynamic? responseData = JsonConvert.DeserializeObject(responseString);
                    if (responseData?.choices?[0]?.text != null)
                    {
                        return responseData.choices[0].text.ToString();
                    }
                    return "生成失败：返回内容为空";
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"API请求失败: {response.StatusCode} - {errorContent}");
                }
            }
        }
    }
}